<template>
	<view class="container">
		<!-- 启动界面 -->
		<view v-if="showStartOverlay" class="start-overlay" @tap="startDialogue">
			<view class="start-btn" @tap.stop="startDialogue"
				  :style="{background:'linear-gradient(45deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}">
				<text class="icon">▶</text>
				<text class="btn-text">开启时空连接</text>
			</view>
		</view>

		<!-- 粒子背景容器 -->
		<canvas canvas-id="particlesCanvas" class="particles-canvas" 
				:style="{width: canvasWidth + 'px', height: canvasHeight + 'px'}"></canvas>
		
		<!-- 装饰性背景 -->
		<view class="bg-grid"></view>
		<view class="bg-circles"></view>
		
		<!-- 对话控制台 -->
		<view v-if="!showStartOverlay" class="dialogue-console">
			<!-- 时间显示 -->
			<view class="time-display">
				<text class="year">2049</text>
				<text class="date">{{currentDate}}</text>
			</view>
			
			<!-- AI头像区域 -->
			<view class="ai-avatar-section">
				<view class="ai-avatar">
					<view class="avatar-glow"></view>
					<view class="ai-face">
						<view class="ai-eye left-eye" :class="{speaking: isAISpeaking}"></view>
						<view class="ai-eye right-eye" :class="{speaking: isAISpeaking}"></view>
						<view class="ai-mouth" :class="{speaking: isAISpeaking}"></view>
						<view class="neural-network">
							<view class="neural-node"></view>
							<view class="neural-node"></view>
							<view class="neural-node"></view>
							<view class="neural-connection"></view>
							<view class="neural-connection"></view>
						</view>
					</view>
				</view>
				<view class="ai-name">
					<text class="ai-title" :style="{color:t('color1')}">明日萌像 AI</text>
					<text class="ai-subtitle">2049年智能对话系统</text>
				</view>
			</view>
			
			<!-- 对话区域 -->
			<view class="dialogue-area">
				<view class="dialogue-header">
					<view class="header-left">
						<text class="header-icon">💬</text>
						<text class="header-text">时空对话进行中...</text>
					</view>
					<view class="header-right">
						<view class="connection-status">
							<view class="status-dot"></view>
							<text class="status-text">已连接</text>
						</view>
						<!-- 清空数据按钮 -->
						<button class="clear-btn" @tap="showClearDialog" type="default">
							🗑️
						</button>
					</view>
				</view>
				
				<!-- 消息容器 -->
				<scroll-view class="messages-container"
							 scroll-y="true"
							 :scroll-top="scrollTop"
							 :scroll-with-animation="true"
							 :enable-back-to-top="false"
							 :scroll-anchoring="true"
							 :scroll-into-view="scrollIntoView">
					<view v-for="(message, index) in messages" :key="index"
						  :id="'message-' + index"
						  :class="['message', message.type === 'ai' ? 'ai-message' : 'user-message']">
						<view class="message-avatar">
							<text class="avatar-icon">{{message.type === 'ai' ? '🤖' : '👤'}}</text>
						</view>
						<view class="message-content">
							<view class="message-text">
								<text>{{message.text}}</text>
								<text v-if="message.typing" class="typing-cursor">|</text>
							</view>
							<view v-if="message.type === 'ai' && message.showPlayBtn" class="play-audio-btn" @tap="playAudio(index)">
								<text class="play-icon">▶</text>
								<text class="play-text">播放</text>
							</view>
						</view>
						<view class="message-time">
							<text>{{message.time}}</text>
						</view>
					</view>

					<!-- 滚动锚点 -->
					<view id="scroll-bottom" class="scroll-anchor"></view>
				</scroll-view>
				
				<!-- 输入区域 -->
				<view v-if="showInput" class="input-area">
					<view class="input-container">
						<input class="user-input"
							   v-model="userInput"
							   :placeholder="inputPlaceholder"
							   placeholder-style="color:#B2B5BE;font-size:28rpx"
							   :maxlength="100"
							   @confirm="sendMessage"
							   @input="onInputChange"
							   @focus="onInputFocus"
							   @blur="onInputBlur"
							   :focus="inputFocus"
							   confirm-type="send" />
						<view class="send-btn" @tap="sendMessage" :class="{disabled: !userInput.trim()}">
							<text class="send-icon">📤</text>
						</view>
					</view>
					<view class="input-tip">
						<text class="tip-text">输入完成后点击发送按钮或按回车键</text>
					</view>
				</view>
			</view>
			
			<!-- 进度指示器 -->
			<view class="progress-indicator">
				<view v-for="(step, index) in progressSteps" :key="index"
					  :class="['progress-step', {active: currentStep === index + 1, completed: currentStep > index + 1}]">
					<view class="step-number">{{index + 1}}</view>
					<view class="step-label">{{step}}</view>
				</view>
				<view class="progress-line" :style="{width: progressWidth}"></view>
			</view>
			
			<!-- 控制按钮 -->
			<view class="dialogue-controls">
				<view class="control-btn" @tap="goBack">
					<text class="btn-icon">←</text>
					<text class="btn-text">返回方舟</text>
				</view>
				<view class="control-btn" @tap="skipQuestion">
					<text class="btn-icon">⏭</text>
					<text class="btn-text">跳过</text>
				</view>
				<view class="control-btn clear-btn" @tap="clearAndRestart">
					<text class="btn-icon">🔄</text>
					<text class="btn-text">清空重新开始</text>
				</view>
			</view>
		</view>
		
		<!-- 页脚信息 -->
		<view class="footer">
			<text class="footer-text">时空对话系统 v2049.1 | 与未来自己的深度交流 | 数据加密传输中...</text>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			showStartOverlay: true,
			canvasWidth: 375,
			canvasHeight: 667,
			currentDate: '',
			isAISpeaking: false,
			messages: [],
			userInput: '',
			inputPlaceholder: '请输入您的回答...',
			inputFocus: false,
			showInput: false,
			scrollTop: 0,
			scrollIntoView: '',
			currentStep: 1,
			progressSteps: ['姓名', '年龄', '梦想'],
			progressWidth: '33%',
			
			// 对话流程
			questions: [
				{
					text: '你好！我是来自2049年的AI助手。首先，请告诉我您的姓名？',
					key: 'name',
					placeholder: '请输入您的姓名...',
					audioFile: '/static/MP3/jiaoshenme.mp3'
				},
				{
					text: '很高兴认识您！请问您现在多少岁了？',
					key: 'age',
					placeholder: '请输入您的年龄...',
					audioFile: '/static/MP3/jisuile.mp3'
				},
				{
					text: '时光荏苒，请分享一下您最大的梦想是什么？',
					key: 'dream',
					placeholder: '请输入您的梦想...',
					audioFile: '/static/MP3/mengxiangshishenme.mp3'
				}
			],
			currentQuestionIndex: 0,
			dialogueData: {},
			typingTimer: null,
			pre_url: '' // 云资源域名前缀
		}
	},
	onLoad() {
		console.log('对话页面加载');

		// 初始化云资源前缀URL
		const app = getApp();
		this.pre_url = app.globalData.pre_url || '';

		this.initCanvas();
		this.initCurrentDate();
		this.checkExistingData();
	},
	onReady() {
		// 初始化完成
	},
	onUnload() {
		this.clearTypingTimer();
	},
	methods: {
		// 初始化画布
		initCanvas() {
			const systemInfo = uni.getSystemInfoSync();
			this.canvasWidth = systemInfo.windowWidth;
			this.canvasHeight = systemInfo.windowHeight;
		},
		
		// 初始化当前日期
		initCurrentDate() {
			const now = new Date();
			const year = now.getFullYear();
			const month = String(now.getMonth() + 1).padStart(2, '0');
			const day = String(now.getDate()).padStart(2, '0');
			this.currentDate = `${year}.${month}.${day}`;
		},
		
		// 检查已有数据
		checkExistingData() {
			const savedData = uni.getStorageSync('user_dialogue_data');
			console.log('检查已有数据:', savedData);

			if (savedData) {
				this.dialogueData = savedData;
				this.currentQuestionIndex = Object.keys(savedData).length;
				console.log('当前问题索引:', this.currentQuestionIndex);

				if (this.currentQuestionIndex >= this.questions.length) {
					// 对话已完成，跳转到拍照页面
					console.log('对话已完成，跳转到拍照页面');
					this.goToCamera();
					return;
				}
				this.showStartOverlay = false;
				this.loadExistingMessages();
			} else {
				// 新用户，自动开始对话（从首页跳转过来）
				console.log('新用户，自动开始对话');
				this.showStartOverlay = false;
				setTimeout(() => {
					this.askNextQuestion();
				}, 1000);
			}
		},
		
		// 加载已有消息
		loadExistingMessages() {
			this.messages = [];
			for (let i = 0; i < this.currentQuestionIndex; i++) {
				const question = this.questions[i];
				// 添加AI问题
				this.messages.push({
					type: 'ai',
					text: question.text,
					time: this.getCurrentTime(),
					showPlayBtn: true
				});
				// 添加用户回答
				this.messages.push({
					type: 'user',
					text: this.dialogueData[question.key],
					time: this.getCurrentTime()
				});
			}
			this.updateProgress();
			this.askNextQuestion();
		},
		
		// 开始对话
		startDialogue() {
			this.showStartOverlay = false;
			setTimeout(() => {
				this.askNextQuestion();
			}, 500);
		},
		
		// 询问下一个问题
		askNextQuestion() {
			console.log('询问下一个问题，当前索引:', this.currentQuestionIndex);

			if (this.currentQuestionIndex >= this.questions.length) {
				console.log('所有问题已完成');
				this.completeDialogue();
				return;
			}

			const question = this.questions[this.currentQuestionIndex];
			console.log('当前问题:', question);
			this.inputPlaceholder = question.placeholder;

			// 添加AI消息
			const aiMessage = {
				type: 'ai',
				text: '',
				time: this.getCurrentTime(),
				typing: true,
				showPlayBtn: false,
				audioFile: question.audioFile
			};
			this.messages.push(aiMessage);
			this.scrollToBottom();

			// 打字效果
			this.isAISpeaking = true;
			this.typeMessage(question.text, aiMessage, () => {
				this.isAISpeaking = false;
				aiMessage.typing = false;
				aiMessage.showPlayBtn = true;
				this.showInput = true;
				this.inputFocus = true;

				// 确保输入框获得焦点
				this.$nextTick(() => {
					this.inputFocus = false;
					setTimeout(() => {
						this.inputFocus = true;
					}, 100);
				});
			});
		},
		
		// 打字效果
		typeMessage(text, messageObj, callback) {
			this.clearTypingTimer();
			let index = 0;
			
			this.typingTimer = setInterval(() => {
				if (index < text.length) {
					messageObj.text += text[index];
					index++;
					this.scrollToBottom();
				} else {
					this.clearTypingTimer();
					if (callback) callback();
				}
			}, 80);
		},
		
		// 输入变化处理
		onInputChange(e) {
			this.userInput = e.detail.value;
		},

		// 输入框获得焦点
		onInputFocus() {
			console.log('输入框获得焦点');
		},

		// 输入框失去焦点
		onInputBlur() {
			console.log('输入框失去焦点');
		},

		// 发送消息
		sendMessage() {
			const input = this.userInput.trim();
			if (!input) {
				uni.showToast({
					title: '请输入内容',
					icon: 'none'
				});
				return;
			}

			// 添加用户消息
			this.messages.push({
				type: 'user',
				text: input,
				time: this.getCurrentTime()
			});

			// 立即滚动到底部显示用户消息
			this.$nextTick(() => {
				this.scrollToBottom();
			});

			// 保存数据
			const question = this.questions[this.currentQuestionIndex];
			this.dialogueData[question.key] = input;
			this.saveDialogueData();

			// 清空输入
			this.userInput = '';
			this.showInput = false;
			this.inputFocus = false;

			// 下一个问题
			this.currentQuestionIndex++;
			this.updateProgress();

			// 再次确保滚动到底部
			setTimeout(() => {
				this.scrollToBottom();
			}, 100);

			setTimeout(() => {
				this.askNextQuestion();
			}, 1000);
		},
		
		// 更新进度
		updateProgress() {
			this.currentStep = this.currentQuestionIndex + 1;
			const progress = Math.min((this.currentQuestionIndex / this.questions.length) * 100, 100);
			this.progressWidth = progress + '%';
		},
		
		// 完成对话
		completeDialogue() {
			// 添加完成消息
			this.messages.push({
				type: 'ai',
				text: '感谢您的分享！现在让我们进入下一个环节，通过AI技术预测您20年后的样子。',
				time: this.getCurrentTime(),
				showPlayBtn: true
			});
			
			this.scrollToBottom();
			
			setTimeout(() => {
				this.goToCamera();
			}, 3000);
		},
		
		// 跳转到拍照页面
		goToCamera() {
			uni.navigateTo({
				url: '/pagesB/dreamark/camera-new'
			});
		},
		
		// 保存对话数据
		saveDialogueData() {
			try {
				uni.setStorageSync('user_dialogue_data', this.dialogueData);
			} catch (e) {
				console.error('保存对话数据失败:', e);
			}
		},
		
		// 获取当前时间
		getCurrentTime() {
			const now = new Date();
			const hours = String(now.getHours()).padStart(2, '0');
			const minutes = String(now.getMinutes()).padStart(2, '0');
			return `${hours}:${minutes}`;
		},
		
		// 滚动到底部
		scrollToBottom() {
			this.$nextTick(() => {
				// 方法1: 滚动到底部锚点
				this.scrollIntoView = 'scroll-bottom';
				console.log('滚动到底部锚点');

				// 方法2: 使用scroll-top作为备用
				const timestamp = Date.now();
				setTimeout(() => {
					this.scrollTop = 999999 + timestamp;
					console.log('备用滚动，scrollTop:', this.scrollTop);
				}, 100);

				// 方法3: 清空scroll-into-view，然后再次设置
				setTimeout(() => {
					this.scrollIntoView = '';
					this.$nextTick(() => {
						this.scrollIntoView = 'scroll-bottom';
					});
				}, 200);

				// 方法4: 最终确保滚动
				setTimeout(() => {
					this.scrollTop = 999999 + timestamp + 1000;
				}, 500);

				console.log('滚动到底部，消息数量:', this.messages.length);
			});
		},
		
		// 清除打字定时器
		clearTypingTimer() {
			if (this.typingTimer) {
				clearInterval(this.typingTimer);
				this.typingTimer = null;
			}
		},
		
		// 播放音频
		playAudio(index) {
			try {
				// 获取对应的消息
				const message = this.messages[index];
				if (!message || message.type !== 'ai') {
					uni.showToast({
						title: '无法播放此消息',
						icon: 'none'
					});
					return;
				}

				// 计算问题索引（AI消息在奇数位置）
				const questionIndex = Math.floor(index / 2);
				const question = this.questions[questionIndex];

				if (!question || !question.audioFile) {
					uni.showToast({
						title: '音频文件不存在',
						icon: 'none'
					});
					return;
				}

				// 创建音频上下文
				const audioContext = uni.createInnerAudioContext();

				// 设置音频源（使用云端资源）
				audioContext.src = this.pre_url + question.audioFile;

				// 音频播放事件
				audioContext.onPlay(() => {
					console.log('开始播放音频:', question.audioFile);
					uni.showToast({
						title: '正在播放语音',
						icon: 'none',
						duration: 1000
					});
				});

				// 音频播放完成事件
				audioContext.onEnded(() => {
					console.log('音频播放完成');
					audioContext.destroy();
				});

				// 音频播放错误事件
				audioContext.onError((res) => {
					console.error('音频播放错误:', res);
					uni.showToast({
						title: '音频播放失败',
						icon: 'none'
					});
					audioContext.destroy();
				});

				// 开始播放
				audioContext.play();

			} catch (error) {
				console.error('播放音频时发生错误:', error);
				uni.showToast({
					title: '播放失败',
					icon: 'none'
				});
			}
		},
		
		// 跳过问题
		skipQuestion() {
			if (this.currentQuestionIndex < this.questions.length) {
				const question = this.questions[this.currentQuestionIndex];
				this.dialogueData[question.key] = '跳过';
				this.saveDialogueData();

				this.userInput = '';
				this.showInput = false;
				this.currentQuestionIndex++;
				this.updateProgress();

				setTimeout(() => {
					this.askNextQuestion();
				}, 500);
			}
		},

		// 显示清空确认对话框
		showClearDialog() {
			console.log('点击清空按钮');

			// 添加触觉反馈
			// #ifdef APP-PLUS
			if (uni.vibrateShort) {
				uni.vibrateShort();
			}
			// #endif

			uni.showModal({
				title: '清空对话数据',
				content: '确定要清空所有对话数据吗？清空后将重新开始对话流程。',
				confirmText: '确定清空',
				cancelText: '取消',
				confirmColor: '#ff4444',
				success: (res) => {
					if (res.confirm) {
						this.clearDialogueData();
					}
				}
			});
		},

		// 清空按钮触摸开始
		onClearTouchStart() {
			console.log('清空按钮触摸开始');
		},

		// 清空按钮触摸结束
		onClearTouchEnd() {
			console.log('清空按钮触摸结束');
		},

		// 清空对话数据
		clearDialogueData() {
			try {
				// 清空本地存储的对话数据
				uni.removeStorageSync('user_dialogue_data');

				// 重置页面数据
				this.dialogueData = {};
				this.messages = [];
				this.currentQuestionIndex = 0;
				this.showInput = false;
				this.userInput = '';
				this.scrollTop = 0;
				this.scrollIntoView = '';

				// 显示成功提示
				uni.showToast({
					title: '数据已清空',
					icon: 'success',
					duration: 2000
				});

				// 延迟后重新开始对话
				setTimeout(() => {
					this.askNextQuestion();
				}, 2000);

				console.log('对话数据已清空，重新开始');
			} catch (e) {
				console.error('清空数据失败:', e);
				uni.showToast({
					title: '清空失败',
					icon: 'error'
				});
			}
		},
		
		// 返回
		goBack() {
			uni.navigateBack();
		},
		
		// 清空重新开始
		clearAndRestart() {
			uni.showModal({
				title: '确认清空',
				content: '确定要清空所有对话记录重新开始吗？',
				success: (res) => {
					if (res.confirm) {
						this.clearAllData();
					}
				}
			});
		},
		
		// 清空所有数据
		clearAllData() {
			try {
				uni.removeStorageSync('user_dialogue_data');
				this.dialogueData = {};
				this.messages = [];
				this.currentQuestionIndex = 0;
				this.currentStep = 1;
				this.progressWidth = '33%';
				this.showStartOverlay = true;
				this.showInput = false;
				
				uni.showToast({
					title: '已清空重新开始',
					icon: 'success'
				});
			} catch (e) {
				uni.showToast({
					title: '清空失败',
					icon: 'error'
				});
			}
		}
	}
}
</script>

<style>
/* 基础样式 */
page {
	background: #0a0a2a;
	color: #ffffff;
	font-family: 'PingFang SC', sans-serif;
}

.container {
	position: relative;
	min-height: 100vh;
	background: linear-gradient(135deg, #0a0a2a 0%, #1a1a3a 30%, #2a2a4a 70%, #1a1a3a 100%);
	overflow: hidden;
	/* 添加微妙的纹理效果 */
	background-image:
		radial-gradient(circle at 25% 75%, rgba(0, 247, 255, 0.02) 0%, transparent 50%),
		radial-gradient(circle at 75% 25%, rgba(189, 0, 255, 0.02) 0%, transparent 50%);
}

/* 启动覆盖层 */
.start-overlay {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background: rgba(10, 10, 42, 0.7);
	display: flex;
	justify-content: center;
	align-items: center;
	z-index: 1000;
	backdrop-filter: blur(5px);
}

.start-btn {
	background: linear-gradient(45deg, #ff00c8, #bd00ff);
	color: #fff;
	padding: 40rpx 90rpx;
	border-radius: 100rpx;
	display: flex;
	align-items: center;
	gap: 30rpx;
	box-shadow: 0 0 50rpx rgba(255, 0, 200, 0.5);
	animation: pulse 2s infinite;
}

.start-btn .icon {
	font-size: 40rpx;
}

.start-btn .btn-text {
	font-size: 30rpx;
	font-weight: bold;
}

@keyframes pulse {
	0% { transform: scale(0.95); }
	70% { transform: scale(1); }
	100% { transform: scale(0.95); }
}

/* 粒子画布 */
.particles-canvas {
	position: fixed;
	top: 0;
	left: 0;
	z-index: 1;
	pointer-events: none;
}

/* 装饰背景 */
.bg-grid {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background-image: 
		linear-gradient(rgba(0, 247, 255, 0.1) 1px, transparent 1px),
		linear-gradient(90deg, rgba(0, 247, 255, 0.1) 1px, transparent 1px);
	background-size: 50px 50px;
	z-index: 2;
	animation: gridMove 20s linear infinite;
}

.bg-circles {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	z-index: 2;
}

@keyframes gridMove {
	0% { transform: translate(0, 0); }
	100% { transform: translate(50px, 50px); }
}

/* 对话控制台 */
.dialogue-console {
	position: relative;
	z-index: 10;
	padding: 40rpx;
	height: 100vh;
	display: flex;
	flex-direction: column;
}

/* 时间显示 */
.time-display {
	text-align: center;
	margin-bottom: 40rpx;
}

.year {
	display: block;
	font-size: 48rpx;
	font-weight: bold;
	color: #00f7ff;
	margin-bottom: 10rpx;
}

.date {
	display: block;
	font-size: 24rpx;
	color: #7df9ff;
}

/* AI头像区域 */
.ai-avatar-section {
	display: flex;
	align-items: center;
	gap: 30rpx;
	margin-bottom: 40rpx;
	padding: 30rpx;
	background: rgba(0, 0, 0, 0.3);
	border: 1px solid rgba(0, 247, 255, 0.3);
	border-radius: 20rpx;
}

.ai-avatar {
	position: relative;
}

.avatar-glow {
	position: absolute;
	top: -10rpx;
	left: -10rpx;
	right: -10rpx;
	bottom: -10rpx;
	background: radial-gradient(circle, rgba(0, 247, 255, 0.3), transparent);
	border-radius: 50%;
	animation: avatarGlow 2s infinite;
}

@keyframes avatarGlow {
	0%, 100% { opacity: 0.5; }
	50% { opacity: 1; }
}

.ai-face {
	width: 120rpx;
	height: 120rpx;
	background: linear-gradient(45deg, #333, #666);
	border-radius: 50%;
	position: relative;
	border: 2px solid #00f7ff;
	box-shadow: 0 0 30rpx rgba(0, 247, 255, 0.5);
}

.ai-eye {
	position: absolute;
	width: 20rpx;
	height: 20rpx;
	background: #00f7ff;
	border-radius: 50%;
	top: 35rpx;
	box-shadow: 0 0 10rpx #00f7ff;
}

.left-eye {
	left: 30rpx;
}

.right-eye {
	right: 30rpx;
}

.ai-eye.speaking {
	animation: eyeBlink 0.5s infinite;
}

.ai-mouth {
	position: absolute;
	bottom: 30rpx;
	left: 50%;
	transform: translateX(-50%);
	width: 30rpx;
	height: 8rpx;
	background: #ff00c8;
	border-radius: 10rpx;
}

.ai-mouth.speaking {
	animation: mouthMove 0.3s infinite;
}

@keyframes eyeBlink {
	0%, 90% { transform: scaleY(1); }
	95% { transform: scaleY(0.1); }
	100% { transform: scaleY(1); }
}

@keyframes mouthMove {
	0%, 100% { transform: translateX(-50%) scaleY(1); }
	50% { transform: translateX(-50%) scaleY(1.5); }
}

.neural-network {
	position: absolute;
	top: 10rpx;
	right: 10rpx;
	width: 40rpx;
	height: 40rpx;
}

.neural-node {
	position: absolute;
	width: 6rpx;
	height: 6rpx;
	background: #00ff80;
	border-radius: 50%;
	animation: neuralPulse 1.5s infinite;
}

.neural-node:nth-child(1) {
	top: 0;
	left: 0;
}

.neural-node:nth-child(2) {
	top: 0;
	right: 0;
	animation-delay: 0.5s;
}

.neural-node:nth-child(3) {
	bottom: 0;
	left: 50%;
	transform: translateX(-50%);
	animation-delay: 1s;
}

@keyframes neuralPulse {
	0%, 100% { opacity: 0.3; }
	50% { opacity: 1; }
}

.ai-name {
	flex: 1;
}

.ai-title {
	display: block;
	font-size: 32rpx;
	font-weight: bold;
	color: #00f7ff;
	margin-bottom: 10rpx;
}

.ai-subtitle {
	display: block;
	font-size: 24rpx;
	color: #7df9ff;
}

/* 对话区域 */
.dialogue-area {
	flex: 1;
	display: flex;
	flex-direction: column;
	background: rgba(0, 0, 0, 0.3);
	border: 1px solid rgba(0, 247, 255, 0.3);
	border-radius: 20rpx;
	margin-bottom: 40rpx;
}

.dialogue-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 30rpx;
	border-bottom: 1px solid rgba(0, 247, 255, 0.2);
}

.header-left {
	display: flex;
	align-items: center;
	gap: 20rpx;
}

.header-right {
	display: flex;
	align-items: center;
	gap: 20rpx;
}

.header-icon {
	font-size: 32rpx;
}

.header-text {
	flex: 1;
	font-size: 28rpx;
	color: #7df9ff;
}

.connection-status {
	display: flex;
	align-items: center;
	gap: 10rpx;
}

.status-dot {
	width: 16rpx;
	height: 16rpx;
	background: #00ff80;
	border-radius: 50%;
	animation: statusBlink 1s infinite;
}

@keyframes statusBlink {
	0%, 50% { opacity: 1; }
	51%, 100% { opacity: 0.3; }
}

.status-text {
	font-size: 24rpx;
	color: #00ff80;
}

/* 清空按钮 */
.clear-btn {
	width: 60rpx;
	height: 60rpx;
	background: rgba(255, 68, 68, 0.1) !important;
	border: 1px solid rgba(255, 68, 68, 0.3) !important;
	border-radius: 50% !important;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;
	font-size: 24rpx;
	color: #ff6666;
	padding: 0 !important;
	margin: 0 !important;
	line-height: 1;
}

.clear-btn::after {
	border: none !important;
}

.clear-btn:active {
	transform: scale(0.9);
	background: rgba(255, 68, 68, 0.2) !important;
	border-color: #ff4444 !important;
}

/* 消息容器 */
.messages-container {
	flex: 1;
	padding: 30rpx;
	max-height: 600rpx;
}

/* 滚动锚点 */
.scroll-anchor {
	height: 1rpx;
	width: 100%;
}

.message {
	display: flex;
	gap: 20rpx;
	margin-bottom: 40rpx;
}

.user-message {
	flex-direction: row-reverse;
}

.message-avatar {
	width: 60rpx;
	height: 60rpx;
	background: rgba(0, 247, 255, 0.2);
	border: 1px solid #00f7ff;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	flex-shrink: 0;
}

.user-message .message-avatar {
	background: rgba(255, 0, 200, 0.2);
	border-color: #ff00c8;
}

.avatar-icon {
	font-size: 24rpx;
}

.message-content {
	flex: 1;
	max-width: 500rpx;
}

.message-text {
	background: linear-gradient(135deg, rgba(0, 247, 255, 0.15), rgba(0, 247, 255, 0.05));
	border: 1px solid rgba(0, 247, 255, 0.4);
	border-radius: 25rpx 25rpx 25rpx 8rpx;
	padding: 25rpx 35rpx;
	font-size: 28rpx;
	line-height: 1.6;
	color: #7df9ff;
	margin-bottom: 15rpx;
	box-shadow: 0 8rpx 25rpx rgba(0, 247, 255, 0.1);
	position: relative;
	backdrop-filter: blur(10rpx);
}

.message-text::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	height: 1px;
	background: linear-gradient(90deg, transparent, rgba(0, 247, 255, 0.3), transparent);
}

.user-message .message-text {
	background: linear-gradient(135deg, rgba(255, 0, 200, 0.15), rgba(255, 0, 200, 0.05));
	border-color: rgba(255, 0, 200, 0.4);
	border-radius: 25rpx 25rpx 8rpx 25rpx;
	color: #ffb3e6;
	box-shadow: 0 8rpx 25rpx rgba(255, 0, 200, 0.1);
}

.user-message .message-text::before {
	background: linear-gradient(90deg, transparent, rgba(255, 0, 200, 0.3), transparent);
}

.typing-cursor {
	color: #00f7ff;
	animation: cursorBlink 1s infinite;
}

@keyframes cursorBlink {
	0%, 50% { opacity: 1; }
	51%, 100% { opacity: 0; }
}

.play-audio-btn {
	display: flex;
	align-items: center;
	gap: 10rpx;
	background: rgba(0, 247, 255, 0.1);
	border: 1px solid #00f7ff;
	border-radius: 30rpx;
	padding: 8rpx 20rpx;
	font-size: 24rpx;
	color: #00f7ff;
	width: fit-content;
}

.play-icon {
	font-size: 20rpx;
}

.play-text {
	font-size: 24rpx;
}

.message-time {
	font-size: 20rpx;
	color: #666;
	text-align: right;
}

.user-message .message-time {
	text-align: left;
}

/* 输入区域 */
.input-area {
	padding: 30rpx;
	border-top: 1px solid rgba(0, 247, 255, 0.2);
}

.input-container {
	display: flex;
	gap: 20rpx;
	align-items: center;
}

.user-input {
	flex: 1;
	background: linear-gradient(135deg, rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.4));
	border: 2px solid rgba(0, 247, 255, 0.5);
	border-radius: 50rpx;
	padding: 25rpx 35rpx;
	font-size: 28rpx;
	color: #7df9ff;
	min-height: 80rpx;
	box-sizing: border-box;
	backdrop-filter: blur(10rpx);
	transition: all 0.3s ease;
	position: relative;
}

.user-input::placeholder {
	color: rgba(125, 249, 255, 0.5);
}

.user-input:focus {
	border-color: #00f7ff;
	background: linear-gradient(135deg, rgba(0, 247, 255, 0.1), rgba(0, 0, 0, 0.5));
	box-shadow:
		0 0 30rpx rgba(0, 247, 255, 0.4),
		inset 0 1rpx 0 rgba(255, 255, 255, 0.1);
	transform: translateY(-2rpx);
}

.input-tip {
	text-align: center;
	margin-top: 20rpx;
}

.tip-text {
	font-size: 22rpx;
	color: #7df9ff;
	opacity: 0.7;
}

.send-btn {
	width: 80rpx;
	height: 80rpx;
	background: linear-gradient(45deg, #00f7ff, #bd00ff);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;
}

.send-btn.disabled {
	opacity: 0.5;
	background: #666;
}

.send-icon {
	font-size: 32rpx;
	color: #fff;
}

/* 进度指示器 */
.progress-indicator {
	position: relative;
	display: flex;
	justify-content: space-between;
	margin-bottom: 40rpx;
	padding: 0 40rpx;
}

.progress-step {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 10rpx;
	z-index: 2;
}

.step-number {
	width: 60rpx;
	height: 60rpx;
	background: rgba(0, 0, 0, 0.5);
	border: 2px solid #666;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 24rpx;
	color: #666;
	transition: all 0.3s ease;
}

.progress-step.active .step-number {
	background: linear-gradient(45deg, #00f7ff, #bd00ff);
	border-color: #00f7ff;
	color: #fff;
}

.progress-step.completed .step-number {
	background: #00ff80;
	border-color: #00ff80;
	color: #000;
}

.step-label {
	font-size: 24rpx;
	color: #666;
}

.progress-step.active .step-label,
.progress-step.completed .step-label {
	color: #7df9ff;
}

.progress-line {
	position: absolute;
	top: 30rpx;
	left: 70rpx;
	right: 70rpx;
	height: 4rpx;
	background: #666;
	z-index: 1;
}

.progress-line::after {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	height: 100%;
	background: linear-gradient(90deg, #00f7ff, #bd00ff);
	transition: width 0.5s ease;
}

/* 控制按钮 */
.dialogue-controls {
	display: flex;
	gap: 20rpx;
	justify-content: space-between;
}

.control-btn {
	flex: 1;
	background: rgba(0, 247, 255, 0.1);
	border: 1px solid #00f7ff;
	border-radius: 50rpx;
	padding: 20rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 10rpx;
	transition: all 0.3s ease;
}

.control-btn:active {
	transform: scale(0.95);
	background: rgba(0, 247, 255, 0.2);
}

.clear-btn {
	background: rgba(255, 68, 68, 0.1);
	border-color: #ff4444;
}

.clear-btn:active {
	background: rgba(255, 68, 68, 0.2);
}

.btn-icon {
	font-size: 24rpx;
}

.btn-text {
	font-size: 24rpx;
	color: #7df9ff;
}

.clear-btn .btn-text {
	color: #ff4444;
}

/* 页脚 */
.footer {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	padding: 20rpx;
	background: rgba(0, 0, 0, 0.8);
	border-top: 1px solid rgba(0, 247, 255, 0.3);
	z-index: 100;
}

.footer-text {
	text-align: center;
	font-size: 20rpx;
	color: #7df9ff;
	line-height: 1.4;
}
</style>
